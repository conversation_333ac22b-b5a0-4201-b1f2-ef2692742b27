import { AxiosInstance } from '.'
import { notify, simplifyBackendError } from '../../shared/helpers/util'

interface I_GetLeadSources {
  skip?: string
  limit?: string
  search?: string
  active?: boolean
}

interface I_CreateLeadSource {
  name: string
  description: string
  channelId: string

  createdBy: string
}

interface I_DeleteLeadSource {
  id: string
}

interface I_RestoreLeadSource {
  id: string
}

interface I_UpdateLeadSource {
  leadSourceId: string
  name: string
  description: string
  channelId: string

  createdBy: string
}

export const getLeadSources = async (data: I_GetLeadSources, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/lead-source/get-lead-source/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        skip: data.skip,
        limit: data.limit,
        search: data.search,
        active: data.active,
      },
    })

    return response
  } catch (error: any) {
    console.error('getLeadSources error', error)
    return error?.response
  }
}

export const createLeadSource = async (data: I_CreateLeadSource) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/lead-source/create-lead-source`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.error('createLeadSource error', error)
    return error?.response
  }
}

export const deleteLeadSource = async (data: I_DeleteLeadSource) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/lead-source/delete-lead-source`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      data: data,
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.error('deleteLeadSource error', error)
    return error?.response
  }
}

export const restoreLeadSource = async (data: I_RestoreLeadSource) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/lead-source/restore-lead-source`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.error('restoreLeadSource error', error)
    return error?.response
  }
}

export const updateLeadSource = async (data: I_UpdateLeadSource) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/lead-source/update-lead-source`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateLeadSource error', error)
    return error?.response
  }
}

export const getLeadSourceById = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')
    const response = await AxiosInstance.get(`lead-source/get-lead-source-by-id/leadSource/${id}/deleted/false`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getLeadSourceById error', error)
    return error?.response
  }
}

// ==================== Campaign ====================

interface ICampaign {
  name: string
  description: string
  leadSourceId: string
  startMonth: number
  startYear: number
  endMonth?: number
  endYear?: number
  cost: string | number | undefined
  isMonthly: boolean
}

export const getCampaigns = async (data: { search?: string; active?: boolean }, deleted: boolean) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/campaign/all/deleted/${deleted}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        active: data.active,
        search: data.search,
      },
    })

    return response
  } catch (error: any) {
    console.error('getCampaigns error', error)
    return error?.response
  }
}
export const createCampaign = async (data: ICampaign) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/campaign`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('createCampaign error', error)
    return error?.response
  }
}

export const updateCampaign = async (data: ICampaign, id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/campaign/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.error('updateCampaign error', error)
    return error?.response
  }
}

export const deleteCampaign = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/campaign/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.error('deleteCampaign error', error)
    return error?.response
  }
}

export const restoreCampaign = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(
      `/campaign/restore/${id}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.error('restoreCampaign error', error)
    return error?.response
  }
}

export const getAdvertisingCost = async (date: string) => {
  const [year, month] = date.split('-')
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/lead-source/advertising-cost/${month}/${year}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')

    console.error('getAdvertisingCost error', error)
    return error?.response
  }
}

export const createLeadSourceRule = async (data: any) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.post(`/lead-source-rule/create`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('createLeadSourceRule error', error)
    return error?.response
  }
}

export const getLeadSourceRules = async (data: any) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.get(`/lead-source-rule/list`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
      params: {
        leadSourceId: data.leadSourceId,
        deleted: data.deleted,
      },
    })
    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('getLeadSourceRules error', error)
    return error?.response
  }
}

export const updateLeadSourceRule = async (data: any, id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(`/lead-source-rule/id/${id}`, data, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('updateLeadSourceRule error', error)
    return error?.response
  }
}

export const deleteLeadSourceRule = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.delete(`/lead-source-rule/id/${id}`, {
      headers: {
        Authorization: `Bearer ${JSON.parse(token)}`,
      },
    })

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('deleteLeadSourceRule error', error)
    return error?.response
  }
}

export const restoreLeadSourceRule = async (id: string) => {
  try {
    const token: any = localStorage.getItem('token')

    const response = await AxiosInstance.patch(
      `/lead-source-rule/restore/${id}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${JSON.parse(token)}`,
        },
      }
    )

    return response
  } catch (error: any) {
    notify(simplifyBackendError(error?.response?.data?.message), 'error')
    console.error('restoreLeadSourceRule error', error)
    return error?.response
  }
}
