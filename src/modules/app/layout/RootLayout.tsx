import { Outlet, useLocation } from 'react-router-dom'
import AuthLayout from './AuthLayout'
import DashboardLayout from './DashboardLayout'

const RootLayout = () => {
  const { pathname } = useLocation()

  // Helper function to determine if a route should use AuthLayout
  const isAuthRoute = (path: string) => {
    const authPaths = [
      '/',
      '/signin',
      '/signup',
      '/member-signup',
      '/forgot-password',
      '/invitation',
      '/plans',
      '/mobile/signup-success',
    ]
    return (
      authPaths.includes(path) ||
      path.startsWith('/reset-password/') ||
      path.startsWith('/signup/') ||
      path.includes('/share-media/')
    )
  }

  if (isAuthRoute(pathname)) {
    return <AuthLayout />
  } else {
    return <DashboardLayout />
  }
}

export default RootLayout
