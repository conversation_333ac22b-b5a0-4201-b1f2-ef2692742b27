// src/components/FormBuilder/FormBuilder.js
import React, { useEffect, useRef, useState } from 'react'
import $ from 'jquery'
import './styles.css'
import FormPreview from './FormPreview'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import { InputWithValidation } from '../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../styles/styled'
import Button from '../../shared/components/button/Button'
import { FormWrapper, SaperatorDiv } from './style'
import { dayjsFormat, getTrueKeys, isSuccess, notify, reverseToTrue } from '../../shared/helpers/util'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate, useParams } from 'react-router-dom'
import { createCompanyForm, deleteCompnayForm, getCompanyFormById, updateCompanyForm } from '../../logic/apis/form'
import useFetch from '../../logic/apis/useFetch'
import { getTeamMembers } from '../../logic/apis/team'
import DropdownWithCheckboxes from '../../shared/dropdownWithCheckboxes/DropdownWithCheckboxes'
import { SLoader } from '../../shared/components/loader/Loader'
import CheckboxList from '../track/components/CheckboxList'
import { DropdownButton, DropdownContainer, DropdownContent } from '../../shared/dropdownWithCheckboxes/style'
import { CaretSvg } from '../../shared/helpers/images'
import { getPosition } from '../../logic/apis/position'
import { FormAccess } from '../../shared/helpers/constants'
import {
  controlOrder,
  disabledAttributes,
  disabledFieldButtons,
  disabledFields,
  fields,
  replaceFields,
  templates,
  userDefinedAttrs,
} from './constant'
import { TextArea } from '../sales/style'
import { CustomModal } from '../../shared/customModal/CustomModal'
import Modal from '../../shared/customModal/Modal'
import { Title } from '../subscription/components/planCard/style'

// Set up jQuery for global access
window.jQuery = $
window.$ = $

// Required jQuery UI and formBuilder plugins
require('jquery-ui-sortable')
require('formBuilder')

interface I_Form {
  fields: any
  name: string
  companyId: string
  createdBy: string
  isPublished?: boolean
  description: string
  active: boolean
  permissions?: any
}
const FormBuilderInstance = ({ formData }: any) => {
  const fb = useRef(null)
  const formBuilderInstance = useRef(null)
  const [formJSON, setFormJSON] = useState('[]')
  const [showPreview, setShowPreview] = useState(false)
  const [isDraft, setIsDraft] = useState(false)
  const [teamMemberSelected, setTeamMemberSelected] = useState({})
  const [positionSelected, setpositionSelected] = useState({})
  const [salesSelected, setSalesSelected] = useState({})
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [showDropdown, setShowDropdown] = useState({ teamMember: false, position: false, sales: false })

  const valuesObjCleanup: string[] = ['select', 'radio-group', 'checkbox-group', 'autocomplete']
  const [loading, setLoading] = useState({ submit: false, delete: false, copy: false, draft: false })

  const [formDataById, setFormDataById] = useState<I_Form>({
    fields: [],
    name: '',
    companyId: '',
    createdBy: '',
    description: '',
    active: true,
    permissions: {},
  })
  const { builderFormId } = useParams()
  const navigate = useNavigate()

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company
  const dropdownRef = useRef(null)
  const location = useLocation()
  const formDraftDataId = location.state?.formDraftDataId

  const [initialValues, setInitialValues] = useState({
    name: `New Form ${dayjsFormat(new Date(), 'M/D/YY h:m')}`,
    fields: [],
    user: false,
    userManager: false,
    specificPerson: false,
    otherEmails: false,
    otherEmailsValue: '',
  })

  const sectionSchema = Yup.object().shape({
    name: Yup.string().required('Required'),
  })

  const { data: teamMemberData, loading: teamMemberLoading } = useFetch({
    fetchFn: () => getTeamMembers({ limit: 1000, deleted: false }),
  })

  const { data: positionData, loading: positionLoading } = useFetch({
    fetchFn: () => getPosition({ deleted: false, limit: '200' }, false),
  })

  useEffect(() => {
    let lastOpenedFieldId: string | null = null

    formBuilderInstance.current = $(fb.current).formBuilder({
      formData,
      fields,
      templates,
      disabledAttrs: disabledAttributes,
      typeUserAttrs: userDefinedAttrs,
      disableFields: disabledFields,
      controlOrder,
      replaceFields,
      disabledFieldButtons: disabledFieldButtons,
      showActionButtons: false,
      scrollToFieldOnAdd: false,
      fieldButtons: ['edit', 'copy', 'remove'],
      fieldRemoveWarn: true,
      sortableControls: false,
      editOnAdd: true,
      onCloseFieldEdit: function () {
        // If another field was closed by user itself
        lastOpenedFieldId = null
      },
      onOpenFieldEdit: function (editPanel: string) {
        const currentField = $(editPanel).closest('.form-field')
        const currentFieldId = currentField.attr('id')

        // If another field was previously opened and it's not the same
        if (lastOpenedFieldId && lastOpenedFieldId !== currentFieldId) {
          formBuilderInstance.current.actions.toggleFieldEdit(lastOpenedFieldId) // close the last one
        }

        lastOpenedFieldId = currentFieldId

        // Scroll into view smoothly
        setTimeout(() => {
          currentField[0]?.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }, 100) // slight delay to allow panel open animation

        const tryFocusLabel = () => {
          const labelDiv = $(editPanel).find('.fld-label[contenteditable="true"]')
          if (labelDiv.length > 0) {
            labelDiv.focus()
            // Optional: move cursor to end
            const range = document.createRange()
            const sel = window.getSelection()
            range.selectNodeContents(labelDiv[0])
            range.collapse(false)
            sel.removeAllRanges()
            sel.addRange(range)
          } else {
            setTimeout(tryFocusLabel, 50)
          }
        }

        tryFocusLabel()
      },
    })

    return () => {
      // Cleanup
      if (formBuilderInstance.current) {
        formBuilderInstance.current.actions.clearFields()
      }
    }
  }, [formData])

  const handleClear = () => {
    if (formBuilderInstance.current) {
      formBuilderInstance.current.actions.clearFields() // Clears all fields
      setFormJSON(`[]`) // Reset stored form data
    }
  }

  useEffect(() => {
    if (builderFormId) {
      const fetchFormById = async () => {
        try {
          const res = await getCompanyFormById(builderFormId)
          if (isSuccess(res)) {
            const { form } = res?.data?.data
            const { name, permissions } = form || {}
            const { whoReceivesCopy = {} } = permissions || {}
            setFormDataById(form)
            setInitialValues((pre) => ({
              ...pre,
              name: name,
              fields: [],
              user: whoReceivesCopy.user || false,
              userManager: whoReceivesCopy.userManager || false,
              specificPerson: !!whoReceivesCopy.teamMember?.length,
              otherEmails: !!whoReceivesCopy.otherEmail?.length,
              otherEmailsValue: whoReceivesCopy.otherEmail?.join(',') || '',
            }))
            setFormJSON(JSON.stringify(form.fields))
            setTimeout(() => {
              formBuilderInstance.current.actions.setData(JSON.stringify(form.fields)) // Load saved data after initialization
            }, 500)
            setpositionSelected(reverseToTrue(permissions?.whoCanUse || []))
            setTeamMemberSelected(reverseToTrue(whoReceivesCopy?.teamMember || []))
            setSalesSelected(reverseToTrue(permissions?.whereIsUse || []))
          }
        } catch (error) {
          console.log({ error })
        }
      }
      // if (formBuilderInstance.current && builderFormId) {
      fetchFormById()
      // }
    }
  }, [builderFormId])

  useEffect(() => {
    const fetchDraftData = async () => {
      if (formDraftDataId && formBuilderInstance.current) {
        const res = await getCompanyFormById(formDraftDataId)
        if (isSuccess(res)) {
          const { form } = res?.data?.data
          const { name, permissions } = form || {}
          const { whoReceivesCopy = {} } = permissions || {}
          setInitialValues((pre) => ({
            ...pre,
            name: name,
            fields: [],
            user: whoReceivesCopy.user || false,
            userManager: whoReceivesCopy.userManager || false,
            specificPerson: !!whoReceivesCopy.teamMember?.length,
            otherEmails: !!whoReceivesCopy.otherEmail?.length,
            otherEmailsValue: whoReceivesCopy.otherEmail?.join(',') || '',
          }))
          setFormJSON(JSON.stringify(form.fields))
          setTimeout(() => {
            formBuilderInstance.current.actions.setData(JSON.stringify(form.fields)) // Load saved data after initialization
          }, 500)
        }
      }
    }
    fetchDraftData()
  }, [formDraftDataId])

  function cleanLabel(htmlString: string): string {
    if (!htmlString) return ''

    // Remove all <br> tags
    let cleaned = htmlString.replace(/<br\s*\/?>/gi, '')

    // Replace known literal entities like &nbsp;
    cleaned = cleaned.replace(/&nbsp;/g, ' ')

    // Decode other entities using DOMParser
    const parser = new DOMParser()
    const decoded = parser.parseFromString(cleaned, 'text/html').body.textContent || ''

    // Replace any remaining non-breaking spaces
    return decoded.replace(/\u00A0/g, ' ').trim()
  }

  const handleSubmit = async (values: typeof initialValues) => {
    const formDataJSON = formBuilderInstance.current.actions.getData('json')
    const formData = JSON.parse(formDataJSON) || []
    const orderedFormData = formData?.map((v, index: number) => {
      const cleanedLabel = cleanLabel(v.label)

      if (valuesObjCleanup.includes(v.type) && Array.isArray(v.values)) {
        return {
          ...v,
          label: cleanedLabel,
          values: v.values.map((option) => {
            const { value, selected, ...rest } = option
            return { ...rest, label: cleanLabel(option.label) }
          }),
          order: index + 1,
        }
      }

      return {
        ...v,
        label: cleanedLabel,
        order: index + 1,
      }
    })

    // console.log({ orderedFormData })
    // return
    // const orderedFormData = formData?.map((v, index: number) => ({ ...v, order: index + 1 }))
    const data: I_Form = {
      fields: orderedFormData,
      name: values.name,
      companyId: currentCompany._id!,
      createdBy: currentMember._id!,
      description: '',
      isPublished: isDraft ? false : true,
      active: true, // Provide a default value for `active`
      permissions: {
        whereIsUse: getTrueKeys(salesSelected),
        whoCanUse: getTrueKeys(positionSelected),
        whoReceivesCopy: {
          user: values.user || false,
          userManager: values.userManager || false,
          teamMember: getTrueKeys(teamMemberSelected),
          otherEmail: values.otherEmailsValue ? values.otherEmailsValue?.split(',')?.map((item) => item.trim()) : [],
        },
      },
    }
    console.log({ data })

    isDraft ? setLoading((prev) => ({ ...prev, draft: true })) : setLoading((prev) => ({ ...prev, submit: true }))
    try {
      if (isDraft) {
        if (!formDraftDataId) {
          const res = await createCompanyForm(data)
          if (isSuccess(res)) {
            notify('Draft created successfully', 'success')
          }
        } else {
          const res = await updateCompanyForm(data, formDraftDataId)
          if (isSuccess(res)) {
            notify('Draft updated successfully', 'success')
          }
        }
      } else {
        if (!builderFormId && !formDraftDataId) {
          const res = await createCompanyForm(data)
          if (isSuccess(res)) {
            notify('Form created successfully', 'success')
            navigate('/settings/form-builder')
            // localStorage.removeItem(`formDraft-${builderFormId || ''}`)
          }
        } else {
          const res = await updateCompanyForm(data, builderFormId || formDraftDataId)
          if (isSuccess(res)) {
            notify('Form updated successfully', 'success')
            navigate('/settings/form-builder')
            // localStorage.removeItem(`formDraft-${builderFormId || ''}`)
          }
        }
      }
    } catch (err: any) {
      console.log({ err })
    } finally {
      setLoading((prev) => ({ ...prev, submit: false }))
      setLoading((prev) => ({ ...prev, draft: false }))
      setIsDraft(false)
    }
  }

  const handleDeleteForm = async () => {
    try {
      setLoading((prev) => ({ ...prev, delete: true }))
      const res = await deleteCompnayForm(builderFormId!)
      if (isSuccess(res)) {
        navigate('/settings/form-builder')
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setLoading((prev) => ({ ...prev, delete: false }))
    }
  }

  const handleCopyForm = async () => {
    try {
      const data: I_Form = {
        fields: formDataById.fields,
        isPublished: true,
        name: `${formDataById.name} copy`,
        companyId: currentCompany._id!,
        createdBy: currentMember._id!,
        description: '',
        active: formDataById.active, // Provide a default value for `active`
        permissions: formDataById.permissions,
      }

      setLoading((prev) => ({ ...prev, copy: true }))
      const res = await createCompanyForm(data)
      if (isSuccess(res)) {
        notify('Form copied successfully', 'success')
        navigate('/settings/form-builder')
        // localStorage.removeItem(`formDraft-${builderFormId || ''}`)
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setLoading((prev) => ({ ...prev, copy: false }))
    }
  }

  const handleFilterChange = (selectedItems: { [key: string]: boolean }, fn: any) => {
    fn(selectedItems)
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown({ position: false, teamMember: false, sales: false })
      }
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showDropdown])

  return (
    <>
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        onSubmit={handleSubmit}
        validationSchema={sectionSchema}
        validateOnChange={true}
        validateOnBlur={false}
      >
        {(formik) => {
          const { values, errors, touched, resetForm, setFieldValue, handleChange } = formik

          useEffect(() => {
            if (!builderFormId && formBuilderInstance.current) {
              const formDataJSON = formBuilderInstance.current.actions.getData('json')
            }
          }, [formBuilderInstance.current, builderFormId])
          console.log({ values }, formBuilderInstance?.current?.actions?.getData('json'))

          return (
            <FormWrapper>
              <Form className="form">
                <SharedStyled.FlexRow width="100%" padding="20px 0" justifyContent="space-between">
                  <InputWithValidation
                    labelName="Name"
                    stateName="name"
                    maxWidth="50%"
                    value={values.name}
                    error={touched.name && errors.name ? true : false}
                  />

                  <div className="button-wapper">
                    {builderFormId ? (
                      <></>
                    ) : (
                      <Button
                        width="max-content"
                        isLoading={loading.draft}
                        type="submit"
                        onClick={() => setIsDraft(true)}
                      >
                        Save Draft
                      </Button>
                    )}
                    &emsp;
                    <Button isLoading={loading.submit} width="max-content" className="success" type="submit">
                      Publish
                    </Button>
                  </div>
                </SharedStyled.FlexRow>

                <div>
                  <div id="fb-editor" ref={fb} />
                  <SharedStyled.FlexCol margin="10px 0 0 0" gap="10px">
                    <div>
                      <Button
                        type="button"
                        className="gray"
                        width="max-content"
                        onClick={() => {
                          setShowConfirmModal(true)
                        }}
                      >
                        Clear Form
                      </Button>
                    </div>

                    <div style={{ width: '100%' }}>
                      {
                        <SharedStyled.FlexBox gap="10px" alignItems="center">
                          <SharedStyled.Text width="250px" fontSize="16px" fontWeight="600">
                            Where is this form used?
                          </SharedStyled.Text>
                          <SharedStyled.FlexCol>
                            <div>
                              <DropdownContainer marginTop="0" ref={dropdownRef}>
                                <DropdownButton
                                  // disabled={uniqueValues.projects?.length === 0}
                                  type="button"
                                  onClick={() => setShowDropdown((p) => ({ ...p, sales: !p.sales }))}
                                  // style={{ width: '300px' }}
                                >
                                  <div className="selection">
                                    All
                                    <img className={showDropdown.sales ? 'rotate' : ''} src={CaretSvg} alt="chevron" />
                                  </div>
                                </DropdownButton>
                              </DropdownContainer>

                              {showDropdown.sales ? (
                                <DropdownContent>
                                  <SharedStyled.FlexBox
                                    gap="0px"
                                    justifyContent="flex-start"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <CheckboxList
                                      className="small"
                                      title="All"
                                      data={
                                        (Object.keys(FormAccess) as Array<keyof typeof FormAccess>).map((key) => ({
                                          name: key,
                                          _id: FormAccess[key],
                                        })) || []
                                      }
                                      checkedItems={salesSelected}
                                      allText="All"
                                      onSelectionChange={(val) => {
                                        handleFilterChange(val, setSalesSelected)
                                      }}
                                    />
                                  </SharedStyled.FlexBox>
                                </DropdownContent>
                              ) : null}
                            </div>
                          </SharedStyled.FlexCol>
                        </SharedStyled.FlexBox>
                      }
                    </div>

                    <div style={{ width: '100%' }}>
                      {positionLoading ? (
                        <div>
                          <SLoader margin="10px 0 0 0" height={52} width={500} />
                        </div>
                      ) : (
                        <SharedStyled.FlexBox gap="10px" alignItems="center">
                          <SharedStyled.Text width="250px" fontSize="16px" fontWeight="600">
                            Who can use this form?
                          </SharedStyled.Text>
                          <SharedStyled.FlexCol>
                            <div>
                              <DropdownContainer marginTop="0" ref={dropdownRef}>
                                <DropdownButton
                                  // disabled={uniqueValues.projects?.length === 0}
                                  type="button"
                                  onClick={() => setShowDropdown((p) => ({ ...p, position: !p.position }))}
                                  // style={{ width: '300px' }}
                                >
                                  <div className="selection">
                                    Positions
                                    <img
                                      className={showDropdown.position ? 'rotate' : ''}
                                      src={CaretSvg}
                                      alt="chevron"
                                    />
                                  </div>
                                </DropdownButton>
                              </DropdownContainer>

                              {showDropdown.position ? (
                                <DropdownContent>
                                  <SharedStyled.FlexBox
                                    gap="0px"
                                    justifyContent="flex-start"
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    <CheckboxList
                                      className="small"
                                      title="Positions"
                                      data={positionData?.position?.map((v) => ({ ...v, name: v.position })) || []}
                                      checkedItems={positionSelected}
                                      allText="All Positions"
                                      onSelectionChange={(val) => {
                                        handleFilterChange(val, setpositionSelected)
                                      }}
                                    />
                                  </SharedStyled.FlexBox>
                                </DropdownContent>
                              ) : null}
                            </div>
                          </SharedStyled.FlexCol>
                        </SharedStyled.FlexBox>
                      )}
                    </div>

                    <div style={{ width: '100%' }}>
                      {teamMemberLoading ? (
                        <div>
                          <SLoader margin="10px 0 0 0" height={52} width={500} />
                        </div>
                      ) : (
                        <SharedStyled.FlexBox gap="10px" alignItems="center">
                          <SharedStyled.Text width="250px" fontSize="16px" fontWeight="600">
                            Who receives a copy?
                          </SharedStyled.Text>
                          <SharedStyled.FlexCol gap="8px">
                            <div>
                              <label>
                                <Field
                                  type="checkbox"
                                  name="user"
                                  onChange={(e) => setFieldValue('user', e.target.checked)}
                                />
                                User
                              </label>
                            </div>

                            <div>
                              {/* User's Manager Checkbox */}
                              <label>
                                <Field
                                  type="checkbox"
                                  name="userManager"
                                  onChange={(e) => setFieldValue('userManager', e.target.checked)}
                                />
                                User's Manager
                              </label>
                            </div>

                            <div>
                              {/* Specific Person Checkbox */}
                              <label>
                                <Field
                                  type="checkbox"
                                  name="specificPerson"
                                  onChange={(e) => setFieldValue('specificPerson', e.target.checked)}
                                />
                                Specific Person
                              </label>
                            </div>

                            {values?.specificPerson ? (
                              <div>
                                <DropdownContainer marginTop="0" ref={dropdownRef}>
                                  <DropdownButton
                                    // disabled={uniqueValues.projects?.length === 0}
                                    type="button"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      setShowDropdown((p) => ({ ...p, teamMember: !p.teamMember }))
                                    }}
                                    // style={{ width: '300px' }}
                                  >
                                    <div className="selection">
                                      Team Members
                                      <img
                                        className={showDropdown.teamMember ? 'rotate' : ''}
                                        src={CaretSvg}
                                        alt="chevron"
                                      />
                                    </div>
                                  </DropdownButton>
                                </DropdownContainer>

                                {showDropdown.teamMember ? (
                                  <DropdownContent>
                                    <SharedStyled.FlexBox
                                      gap="0px"
                                      justifyContent="flex-start"
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      <CheckboxList
                                        className="small"
                                        title="Team Members"
                                        data={teamMemberData?.memberData || []}
                                        checkedItems={teamMemberSelected}
                                        allText="All Members"
                                        onSelectionChange={(val) => {
                                          handleFilterChange(val, setTeamMemberSelected)
                                        }}
                                      />
                                    </SharedStyled.FlexBox>
                                  </DropdownContent>
                                ) : null}
                              </div>
                            ) : null}

                            <div>
                              <label>
                                <Field
                                  type="checkbox"
                                  name="otherEmails"
                                  onChange={(e) => setFieldValue('otherEmails', e.target.checked)}
                                />
                                Other Emails
                              </label>
                            </div>

                            {values?.otherEmails ? (
                              // <InputWithValidation
                              //   labelName="Enter other emails w/ comma"
                              //   stateName="otherEmailsValue"
                              //   maxWidth="300px"
                              //   value={values.otherEmailsValue}
                              //   error={touched.otherEmailsValue && errors.otherEmailsValue ? true : false}
                              // />

                              <TextArea
                                component="textarea"
                                placeholder="Enter other emails w/ comma"
                                as={Field}
                                name="otherEmailsValue"
                                marginTop="8px"
                                minHeight="48px"
                                stateName="otherEmailsValue"
                                labelName="Enter other emails w/ comma"
                                error={touched.otherEmailsValue && errors.otherEmailsValue ? true : false}
                              />
                            ) : null}
                          </SharedStyled.FlexCol>
                        </SharedStyled.FlexBox>
                      )}
                    </div>
                  </SharedStyled.FlexCol>

                  {/* <button onClick={handleSave}>Save</button> */}
                  <SharedStyled.ButtonContainer marginTop="10px">
                    <div></div>
                    <SaperatorDiv>
                      <Button
                        type="submit"
                        isLoading={loading.copy}
                        onClick={handleCopyForm}
                        className="gray"
                        width="max-content"
                      >
                        Copy Form
                      </Button>
                      {builderFormId && (
                        <Button
                          onClick={handleDeleteForm}
                          className="fit delete"
                          type="button"
                          isLoading={loading.delete}
                        >
                          Delete Form
                        </Button>
                      )}
                    </SaperatorDiv>
                  </SharedStyled.ButtonContainer>
                </div>
              </Form>
            </FormWrapper>
          )
        }}
      </Formik>

      <CustomModal show={showConfirmModal}>
        <Modal title={`Clear Form`} hideCloseButton>
          <SharedStyled.FlexCol alignItems="flex-start" padding="0 0 0 8px">
            <Title style={{ fontSize: '18px', padding: '0px' }}>
              This will delete all existing fields in your form. Are you sure?
            </Title>
            <SharedStyled.FlexRow margin="16px 0 0 0">
              <Button
                className="gray"
                onClick={() => {
                  setShowConfirmModal(false)
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  setShowConfirmModal(false)
                  handleClear()
                }}
              >
                Clear Form
              </Button>
            </SharedStyled.FlexRow>
          </SharedStyled.FlexCol>
        </Modal>
      </CustomModal>
    </>
  )
}

export default FormBuilderInstance
