import { Form, Formik } from 'formik'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'

import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import { createMarketingChannel } from '../../../../logic/apis/marketingChannel'
import { getDataFromLocalStorage, isSuccess, notify } from '../../../../shared/helpers/util'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import * as SharedStyled from '../../../../styles/styled'
import * as Styled from './style'
import UnitSvg from '../../../../assets/newIcons/unitModal.svg'
import { ModalHeaderInfo } from '../../../units/components/newUnitModal/style'
import Button from '../../../../shared/components/button/Button'
import { StorageKey } from '../../../../shared/helpers/constants'
import CustomSelect from '../../../../shared/customSelect/CustomSelect'
import AutoCompleteIndentation from '../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { mergeSourceAndCampaignNames } from '../../../contact/components/addNewContactModal/AddNewContactModal'
import { getFormattedLeadSrcData, getLeadSrcDropdownId, getLeadSrcDropdownName } from '../../../leadSource/LeadSource'
import { createLeadSourceRule, deleteLeadSourceRule, updateLeadSourceRule } from '../../../../logic/apis/leadSource'

interface InitialValues {
  trackingTag: string
  leadValue: string
  leadSourceName: string
}

interface I_CreateRuleDialog {
  leadSrcData: any[]
  trackingData: any
  onComplete: () => void
  onClose: () => void
  ruleData?: any
}
export const convertConditionsToStrings = (conditions: Array<{ field: string; value: string }>) => {
  return conditions.map((condition) => `${condition.field} = ${condition.value}`)
}
export const CreateRuleDialog = (props: I_CreateRuleDialog) => {
  /**
   * This initialValues is the state which is passed to the Formik prop: initialValues
   */

  /**
   * loading will be the loading state when performing the operations
   */
  const [loading, setLoading] = useState<boolean>(false)
  const [loadingDelete, setLoadingDelete] = useState<boolean>(false)
  // State to track selected conditions
  const [selectedConditions, setSelectedConditions] = useState<string[]>([])

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember } = globalSelector.company

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [inputRef])

  const { leadSrcData, onComplete, onClose, trackingData, ruleData } = props

  const [initialValues, setInitialValues] = useState<InitialValues>({
    trackingTag: '',
    leadValue: '',
    leadSourceName: '',
  })

  const MarketingChannelCreationFormSchema = Yup.object().shape({
    trackingTag: Yup.string().trim(),
    leadValue: Yup.string().trim(),
    leadSourceName: Yup.string().trim().required('Required'),
  })

  useEffect(() => {
    // If editing an existing rule, populate the selectedConditions
    if (ruleData && ruleData.conditions && Array.isArray(ruleData.conditions)) {
      setSelectedConditions(convertConditionsToStrings(ruleData.conditions))
    }
    if (ruleData) {
      setInitialValues({
        trackingTag: '',
        leadValue: '',
        leadSourceName:
          getLeadSrcDropdownName(ruleData?.campaignId || ruleData?.leadSourceId, leadSrcData)?.sourceName || '',
      })
    }
  }, [ruleData])

  /**
   * handleSubmit is called when the form is submitted and this function needs to be passed to the Formik prop: onSubmit
   * @param submittedValues are the states which formik keeps track of and its type InitialValues is defined at the very top of this file
   */
  const handleSubmit = async (submittedValues: InitialValues, { resetForm }: any) => {
    try {
      // if (Object.keys(currentCompany).length > 0 && Object.keys(currentMember).length > 0) {
      setLoading(true)
      const result = getLeadSrcDropdownId(submittedValues?.leadSourceName || '', leadSrcData)
      const leadSourceId = result?.leadSourceId
      const campaignId = result?.campaignId || null
      const conditions = selectedConditions.map((condition) => {
        // Split by the equals sign and trim whitespace
        const [field, value] = condition.split('=').map((part) => part.trim())
        return {
          field,
          value,
        }
      })
      let dataObj = {
        conditions,
        leadSourceId,
        campaignId,
        isActive: true,
      }

      if (ruleData) {
        const res = await updateLeadSourceRule(dataObj, ruleData._id)
        if (isSuccess(res)) {
          notify('Rule Updated Successfully', 'success')
          resetForm()
          setLoading(false)
          onComplete()
        } else {
          setLoading(false)
          notify(res?.data?.message, 'error')
        }
      } else {
        const res = await createLeadSourceRule(dataObj)
        if (isSuccess(res)) {
          notify('Rule Created Successfully', 'success')
          resetForm()
          setLoading(false)
          onComplete()
        } else {
          setLoading(false)
          notify(res?.data?.message, 'error')
        }
      }
      // }
    } catch (error) {
      setLoading(false)
      console.error(error)
    }
  }

  const onDelete = async () => {
    try {
      setLoadingDelete(true)
      const res = await deleteLeadSourceRule(ruleData._id)
      if (isSuccess(res)) {
        notify('Rule Deleted Successfully', 'success')
        setLoadingDelete(false)
        onComplete()
      } else {
        setLoadingDelete(false)
        notify(res?.data?.message, 'error')
      }
    } catch (error) {
      setLoadingDelete(false)
      console.error(error)
    }
  }

  return (
    <Styled.CreateRuleDialogContainer>
      {' '}
      <Formik
        initialValues={initialValues}
        onSubmit={handleSubmit}
        validationSchema={MarketingChannelCreationFormSchema}
        validateOnChange={true}
        validateOnBlur={false}
        enableReinitialize={true}
      >
        {/* <Styled.ResetpasswordContainer> */}
        {({ touched, errors, values, resetForm, setFieldValue }) => {
          useEffect(() => {
            setFieldValue('leadValue', trackingData[values.trackingTag])
          }, [values.trackingTag])
          return (
            <>
              <Styled.ModalHeaderContainer>
                <SharedStyled.FlexRow>
                  <img src={UnitSvg} alt="modal icon" />
                  <SharedStyled.FlexCol>
                    <Styled.ModalHeader>{ruleData ? 'Edit' : 'Create'} Matching Rule</Styled.ModalHeader>
                  </SharedStyled.FlexCol>
                </SharedStyled.FlexRow>
                <Styled.CrossContainer
                  onClick={() => {
                    onClose()
                    setLoading(false)
                    resetForm()
                  }}
                >
                  <CrossIcon />
                </Styled.CrossContainer>
              </Styled.ModalHeaderContainer>
              <SharedStyled.SettingModalContentContainer>
                <Form className="form">
                  <SharedStyled.Content maxWidth="706px" width="100%" disableBoxShadow={true} noPadding={true}>
                    <SharedStyled.Text fontSize="16px" fontWeight="700" margin="0 auto 0 0">
                      Conditions
                    </SharedStyled.Text>

                    <SharedStyled.Text fontSize="14px" margin="5px auto 0 0">
                      If a lead comes in that match all of the following:
                    </SharedStyled.Text>
                    {selectedConditions.length > 0 && (
                      <SharedStyled.FlexCol width="100%" margin="0 0 15px 0">
                        <SharedStyled.FlexRow flexWrap="wrap" gap="10px">
                          {selectedConditions.map((condition, index) => (
                            <div
                              key={index}
                              className="tag-option"
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                padding: '5px 10px',
                                background: '#f0f0f0',
                                borderRadius: '4px',
                                margin: '5px 0',
                                fontSize: '14px',
                              }}
                            >
                              {condition}
                              <span
                                style={{ marginLeft: '8px', cursor: 'pointer' }}
                                onClick={() => {
                                  setSelectedConditions((prev) => prev.filter((_, i) => i !== index))
                                }}
                                title="Click the 'x' to remove this condition (can be added back in below)"
                              >
                                <CrossIcon />
                              </span>
                            </div>
                          ))}
                        </SharedStyled.FlexRow>
                      </SharedStyled.FlexCol>
                    )}
                    <SharedStyled.TwoInputDiv>
                      <CustomSelect
                        dropDownData={Object.keys(trackingData)}
                        setValue={() => {}}
                        stateName="trackingTag"
                        value={values.trackingTag}
                        error={touched.trackingTag && errors.trackingTag ? true : false}
                        setFieldValue={setFieldValue}
                        labelName="Tracking Data Tag"
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />
                      <InputWithValidation
                        labelName="Description"
                        stateName="leadValue"
                        value={values.leadValue}
                        error={touched.leadValue && errors.leadValue ? true : false}
                      />

                      <SharedStyled.Button
                        maxWidth="50px"
                        marginTop="7px"
                        mediaHeight="52px"
                        type="button"
                        disabled={!values.trackingTag || !values.leadValue}
                        onClick={() => {
                          // Check if the tracking tag already exists in any condition
                          const tagAlreadyExists = selectedConditions.some((condition) => {
                            const [existingTag] = condition.split('=').map((part) => part.trim())
                            return existingTag === values.trackingTag
                          })

                          if (tagAlreadyExists) {
                            notify(`A condition with tracking tag "${values.trackingTag}" already exists`, 'warning')
                          } else {
                            // Add the condition to tags above
                            const condition = `${values.trackingTag} = ${values.leadValue}`
                            if (condition) {
                              // Add to selected conditions array
                              setSelectedConditions((prev) => [...prev, condition])
                              // Clear the input fields
                              setFieldValue('trackingTag', '')
                              setFieldValue('leadValue', '')
                            }
                          }
                        }}
                        title="Click the '+' button to add that condition to the tags above"
                      >
                        <SharedStyled.IconCode>&#x2B;</SharedStyled.IconCode>
                      </SharedStyled.Button>
                    </SharedStyled.TwoInputDiv>

                    <AutoCompleteIndentation
                      labelName="Lead Source"
                      stateName={`leadSourceName`}
                      isLeadSource
                      dropdownHeight="300px"
                      error={touched.leadSourceName && errors.leadSourceName ? true : false}
                      borderRadius="0px"
                      setFieldValue={setFieldValue}
                      options={mergeSourceAndCampaignNames(leadSrcData)}
                      formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                      value={values.leadSourceName!}
                      setValueOnClick={(val: string) => {
                        setFieldValue('leadSourceName', val)
                      }}
                      className="material-autocomplete"
                      isIndentation={true}
                    />

                    <SharedStyled.ButtonContainer marginTop="26px">
                      {ruleData ? (
                        <Button isLoading={loadingDelete} type="button" className="delete" onClick={() => onDelete()}>
                          Delete
                        </Button>
                      ) : (
                        <Button type="button" className="gray" onClick={() => onClose()}>
                          Cancel
                        </Button>
                      )}

                      <Button type="submit" isLoading={loading}>
                        Save
                      </Button>
                    </SharedStyled.ButtonContainer>
                  </SharedStyled.Content>
                </Form>
              </SharedStyled.SettingModalContentContainer>
            </>
          )
        }}
      </Formik>
    </Styled.CreateRuleDialogContainer>
  )
}
